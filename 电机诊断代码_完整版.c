/*
 * 电机完整诊断代码 - 替换main.c中的USER CODE BEGIN 2到USER CODE END 2部分
 * 包含LED指示、串口测试、逐步电机验证
 */

// 初始化串口和DMA
__HAL_UART_CLEAR_IDLEFLAG(&huart1);
__HAL_UART_ENABLE_IT(&huart1, UART_IT_IDLE);
HAL_UART_Receive_DMA(&huart1, (uint8_t *)rxCmd, CMD_LEN);

HAL_Delay(100); // 等待系统稳定

/**********************************************************
***	第一步：LED诊断 - 验证程序正常运行
**********************************************************/
// LED闪烁3次表示程序启动
for(int i = 0; i < 3; i++) {
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);   // LED灭
    HAL_Delay(300);
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED亮  
    HAL_Delay(300);
}

/**********************************************************
***	第二步：串口原始测试 - 验证硬件通讯
**********************************************************/
// 发送原始字节验证串口硬件
uint8_t raw_test[] = {0xAA, 0x55, 0xFF, 0x00}; // 测试模式
HAL_UART_Transmit(&huart1, raw_test, 4, 1000);
HAL_Delay(100);

// 发送标准使能命令的原始字节
uint8_t enable_raw[] = {0x01, 0xF3, 0xAB, 0x01, 0x00, 0x6B};
HAL_UART_Transmit(&huart1, enable_raw, 6, 1000);
HAL_Delay(500);

/**********************************************************
***	第三步：根据DEBUG_MODE进行不同级别测试
**********************************************************/
#if DEBUG_MODE == 1
    //=== 测试模式1：基础通讯测试 ===
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED常亮表示测试1
    
    // 连续发送使能/禁用命令
    for(int i = 0; i < 5; i++) {
        Emm_V5_En_Control(1, true, false);  // 使能
        HAL_Delay(1000);
        Emm_V5_En_Control(1, false, false); // 禁用
        HAL_Delay(1000);
    }

#elif DEBUG_MODE == 2
    //=== 测试模式2：极低速旋转测试 ===
    // LED慢闪表示测试2
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
    HAL_Delay(500);
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
    HAL_Delay(500);
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
    
    Emm_V5_En_Control(1, true, false);
    HAL_Delay(200);
    
    // 极低速测试：30RPM，最小加速度
    Emm_V5_Vel_Control(1, 0, 30, 1, false);
    HAL_Delay(8000); // 运行8秒观察
    Emm_V5_Stop_Now(1, false);
    HAL_Delay(2000);
    
    // 反方向测试
    Emm_V5_Vel_Control(1, 1, 30, 1, false);
    HAL_Delay(8000);
    Emm_V5_Stop_Now(1, false);

#elif DEBUG_MODE == 3
    //=== 测试模式3：微步位置控制 ===
    // LED快闪表示测试3
    for(int i = 0; i < 3; i++) {
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
        HAL_Delay(100);
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
        HAL_Delay(100);
    }
    
    Emm_V5_En_Control(1, true, false);
    HAL_Delay(200);
    
    // 微小角度测试：100脉冲 ≈ 1/32圈
    Emm_V5_Pos_Control(1, 0, 50, 2, 100, 0, 0);
    HAL_Delay(5000);
    
    // 返回原位
    Emm_V5_Pos_Control(1, 1, 50, 2, 100, 0, 0);
    HAL_Delay(5000);

#elif DEBUG_MODE == 4
    //=== 测试模式4：地址扫描测试 ===
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
    
    // 尝试不同的电机地址
    for(uint8_t addr = 1; addr <= 5; addr++) {
        // LED闪烁次数表示当前测试的地址
        for(int i = 0; i < addr; i++) {
            HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
            HAL_Delay(200);
            HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
            HAL_Delay(200);
        }
        HAL_Delay(1000);
        
        // 测试该地址
        Emm_V5_En_Control(addr, true, false);
        HAL_Delay(500);
        Emm_V5_Vel_Control(addr, 0, 100, 5, false);
        HAL_Delay(2000);
        Emm_V5_Stop_Now(addr, false);
        HAL_Delay(1000);
    }

#else
    //=== 默认模式：保守参数的完整测试 ===
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED常亮
    
    // 使能电机
    Emm_V5_En_Control(1, true, false);
    HAL_Delay(200);
    
    // 保守的位置控制：1圈，低速
    Emm_V5_Pos_Control(1, 0, 200, 5, 3200, 0, 0);
    
    // 延长等待时间到15秒
    uint32_t timeout = HAL_GetTick() + 15000;
    while(rxFrameFlag == false && HAL_GetTick() < timeout) {
        // 等待期间LED闪烁表示程序运行
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
        HAL_Delay(500);
    }
    
    if(HAL_GetTick() >= timeout) {
        // 超时：尝试最基础的速度控制
        HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET); // LED灭表示超时
        Emm_V5_Vel_Control(1, 0, 50, 3, false);
        HAL_Delay(5000);
        Emm_V5_Stop_Now(1, false);
    }
    rxFrameFlag = false;
#endif

/**********************************************************
***	第四步：测试完成指示
**********************************************************/
// 测试完成：LED快速闪烁10次
for(int i = 0; i < 10; i++) {
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
    HAL_Delay(50);
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
    HAL_Delay(50);
}

// 最终状态：LED慢闪表示等待下次复位
HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET); // 最终熄灭

/*
使用说明：
1. 将此代码替换main.c中/* USER CODE BEGIN 2 */到/* USER CODE END 2 */之间的所有内容

2. 在main.h中设置DEBUG_MODE：
   - 1: 基础通讯测试（使能/禁用循环）
   - 2: 极低速旋转测试（30RPM正反转）
   - 3: 微步位置控制（小角度往返）
   - 4: 地址扫描测试（测试地址1-5）
   - 0或其他: 完整保守测试

3. 观察LED指示：
   - 启动时闪3次：程序正常启动
   - 测试期间的不同闪烁模式：表示不同测试阶段
   - 结束时快闪10次：测试完成
   - 最终熄灭：等待复位重新测试

4. 硬件检查要点：
   - 确认PC13有LED（STM32F407开发板通常有）
   - 确认PA9/PA10连接到电机驱动器
   - 确认电机驱动器供电正常
   - 确认电机正确连接到驱动器

5. 如果LED都不闪烁：
   - 检查程序是否正确下载
   - 检查复位是否正常
   - 检查电源供电

6. 如果LED正常但电机不动：
   - 检查串口连接（TX/RX是否接反）
   - 检查电机驱动器地址设置
   - 检查电机驱动器供电电压
   - 使用示波器检查串口信号
*/
