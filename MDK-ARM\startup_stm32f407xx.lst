


ARM Macro Assembler    Page 1 


    1 00000000         ;******************** (C) COPYRIGHT 2017 STMicroelectron
                       ics ********************
    2 00000000         ;* File Name          : startup_stm32f407xx.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Description        : STM32F407xx devices vector table
                        for MDK-ARM toolchain. 
    5 00000000         ;*                      This module performs:
    6 00000000         ;*                      - Set the initial SP
    7 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
    8 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
    9 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   10 00000000         ;*                        calls main()).
   11 00000000         ;*                      After Reset the CortexM4 process
                       or is in Thread mode,
   12 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   13 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>   
   14 00000000         ;*******************************************************
                       ************************
   15 00000000         ; 
   16 00000000         ;* Redistribution and use in source and binary forms, wi
                       th or without modification,
   17 00000000         ;* are permitted provided that the following conditions 
                       are met:
   18 00000000         ;*   1. Redistributions of source code must retain the a
                       bove copyright notice,
   19 00000000         ;*      this list of conditions and the following discla
                       imer.
   20 00000000         ;*   2. Redistributions in binary form must reproduce th
                       e above copyright notice,
   21 00000000         ;*      this list of conditions and the following discla
                       imer in the documentation
   22 00000000         ;*      and/or other materials provided with the distrib
                       ution.
   23 00000000         ;*   3. Neither the name of STMicroelectronics nor the n
                       ames of its contributors
   24 00000000         ;*      may be used to endorse or promote products deriv
                       ed from this software
   25 00000000         ;*      without specific prior written permission.
   26 00000000         ;*
   27 00000000         ;* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AN
                       D CONTRIBUTORS "AS IS"
   28 00000000         ;* AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT
                        NOT LIMITED TO, THE
   29 00000000         ;* IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
                        A PARTICULAR PURPOSE ARE
   30 00000000         ;* DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
                        CONTRIBUTORS BE LIABLE
   31 00000000         ;* FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPL
                       ARY, OR CONSEQUENTIAL
   32 00000000         ;* DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT O
                       F SUBSTITUTE GOODS OR
   33 00000000         ;* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS 
                       INTERRUPTION) HOWEVER
   34 00000000         ;* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CON
                       TRACT, STRICT LIABILITY,



ARM Macro Assembler    Page 2 


   35 00000000         ;* OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING I
                       N ANY WAY OUT OF THE USE
   36 00000000         ;* OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY 
                       OF SUCH DAMAGE.
   37 00000000         ; 
   38 00000000         ;*******************************************************
                       ************************
   39 00000000         
   40 00000000         ; Amount of memory (in bytes) allocated for Stack
   41 00000000         ; Tailor this value to your application needs
   42 00000000         ; <h> Stack Configuration
   43 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   44 00000000         ; </h>
   45 00000000         
   46 00000000 00001000 
                       Stack_Size
                               EQU              0x1000
   47 00000000         
   48 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   49 00000000         Stack_Mem
                               SPACE            Stack_Size
   50 00001000         __initial_sp
   51 00001000         
   52 00001000         
   53 00001000         ; <h> Heap Configuration
   54 00001000         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   55 00001000         ; </h>
   56 00001000         
   57 00001000 00000400 
                       Heap_Size
                               EQU              0x400
   58 00001000         
   59 00001000                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   60 00000000         __heap_base
   61 00000000         Heap_Mem
                               SPACE            Heap_Size
   62 00000400         __heap_limit
   63 00000400         
   64 00000400                 PRESERVE8
   65 00000400                 THUMB
   66 00000400         
   67 00000400         
   68 00000400         ; Vector Table Mapped to Address 0 at Reset
   69 00000400                 AREA             RESET, DATA, READONLY
   70 00000000                 EXPORT           __Vectors
   71 00000000                 EXPORT           __Vectors_End
   72 00000000                 EXPORT           __Vectors_Size
   73 00000000         
   74 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   75 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   76 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   77 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   78 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler



ARM Macro Assembler    Page 3 


                                                            
   79 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   80 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   81 0000001C 00000000        DCD              0           ; Reserved
   82 00000020 00000000        DCD              0           ; Reserved
   83 00000024 00000000        DCD              0           ; Reserved
   84 00000028 00000000        DCD              0           ; Reserved
   85 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   86 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   87 00000034 00000000        DCD              0           ; Reserved
   88 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   89 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   90 00000040         
   91 00000040         ; External Interrupts
   92 00000040 00000000        DCD              WWDG_IRQHandler ; Window WatchD
                                                            og                 
                                                                               
                                                                
   93 00000044 00000000        DCD              PVD_IRQHandler ; PVD through EX
                                                            TI Line detection  
                                                                               
                                                               
   94 00000048 00000000        DCD              TAMP_STAMP_IRQHandler ; Tamper 
                                                            and TimeStamps thro
                                                            ugh the EXTI line  
                                                                      
   95 0000004C 00000000        DCD              RTC_WKUP_IRQHandler ; RTC Wakeu
                                                            p through the EXTI 
                                                            line               
                                                                    
   96 00000050 00000000        DCD              FLASH_IRQHandler ; FLASH       
                                                                               
                                                                             
   97 00000054 00000000        DCD              RCC_IRQHandler ; RCC           
                                                                               
                                                                           
   98 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line0  
                                                                               
                                                                               
                                                                 
   99 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line1  
                                                                               
                                                                               
                                                                 
  100 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line2  
                                                                               
                                                                               
                                                                 
  101 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line3  
                                                                               
                                                                               
                                                                 
  102 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line4  



ARM Macro Assembler    Page 4 


                                                                               
                                                                               
                                                                 
  103 0000006C 00000000        DCD              DMA1_Stream0_IRQHandler ; DMA1 
                                                            Stream 0           
                                                                               
                                                                 
  104 00000070 00000000        DCD              DMA1_Stream1_IRQHandler ; DMA1 
                                                            Stream 1           
                                                                               
                                                                 
  105 00000074 00000000        DCD              DMA1_Stream2_IRQHandler ; DMA1 
                                                            Stream 2           
                                                                               
                                                                 
  106 00000078 00000000        DCD              DMA1_Stream3_IRQHandler ; DMA1 
                                                            Stream 3           
                                                                               
                                                                 
  107 0000007C 00000000        DCD              DMA1_Stream4_IRQHandler ; DMA1 
                                                            Stream 4           
                                                                               
                                                                 
  108 00000080 00000000        DCD              DMA1_Stream5_IRQHandler ; DMA1 
                                                            Stream 5           
                                                                               
                                                                 
  109 00000084 00000000        DCD              DMA1_Stream6_IRQHandler ; DMA1 
                                                            Stream 6           
                                                                               
                                                                 
  110 00000088 00000000        DCD              ADC_IRQHandler ; ADC1, ADC2 and
                                                             ADC3s             
                                                                           
  111 0000008C 00000000        DCD              CAN1_TX_IRQHandler ; CAN1 TX   
                                                                               
                                                                               
                                                                   
  112 00000090 00000000        DCD              CAN1_RX0_IRQHandler ; CAN1 RX0 
                                                                               
                                                                               
                                                                    
  113 00000094 00000000        DCD              CAN1_RX1_IRQHandler ; CAN1 RX1 
                                                                               
                                                                               
                                                                    
  114 00000098 00000000        DCD              CAN1_SCE_IRQHandler ; CAN1 SCE 
                                                                               
                                                                               
                                                                    
  115 0000009C 00000000        DCD              EXTI9_5_IRQHandler ; External L
                                                            ine[9:5]s          
                                                                               
                                                                   
  116 000000A0 00000000        DCD              TIM1_BRK_TIM9_IRQHandler ; TIM1
                                                             Break and TIM9    
                                                                           
  117 000000A4 00000000        DCD              TIM1_UP_TIM10_IRQHandler ; TIM1
                                                             Update and TIM10  



ARM Macro Assembler    Page 5 


                                                                           
  118 000000A8 00000000        DCD              TIM1_TRG_COM_TIM11_IRQHandler ;
                                                             TIM1 Trigger and C
                                                            ommutation and TIM1
                                                            1
  119 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare         
                                                                               
                                                                   
  120 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2         
                                                                               
                                                                            
  121 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3         
                                                                               
                                                                            
  122 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4         
                                                                               
                                                                            
  123 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                                               
                                                                               
                                                                   
  124 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                                               
                                                                               
                                                                   
  125 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                                               
                                                                               
                                                                   
  126 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                                               
                                                                               
                                                                     
  127 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1         
                                                                               
                                                                            
  128 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2         
                                                                               
                                                                            
  129 000000D4 00000000        DCD              USART1_IRQHandler ; USART1     
                                                                               
                                                                              
  130 000000D8 00000000        DCD              USART2_IRQHandler ; USART2     
                                                                               
                                                                              
  131 000000DC 00000000        DCD              USART3_IRQHandler ; USART3     
                                                                               
                                                                              
  132 000000E0 00000000        DCD              EXTI15_10_IRQHandler ; External
                                                             Line[15:10]s      
                                                                               
                                                                     
  133 000000E4 00000000        DCD              RTC_Alarm_IRQHandler ; RTC Alar
                                                            m (A and B) through
                                                             EXTI Line         
                                                                     
  134 000000E8 00000000        DCD              OTG_FS_WKUP_IRQHandler ; USB OT
                                                            G FS Wakeup through



ARM Macro Assembler    Page 6 


                                                             EXTI line         
                                                                           
  135 000000EC 00000000        DCD              TIM8_BRK_TIM12_IRQHandler ; TIM
                                                            8 Break and TIM12  
                                                                            
  136 000000F0 00000000        DCD              TIM8_UP_TIM13_IRQHandler ; TIM8
                                                             Update and TIM13  
                                                                           
  137 000000F4 00000000        DCD              TIM8_TRG_COM_TIM14_IRQHandler ;
                                                             TIM8 Trigger and C
                                                            ommutation and TIM1
                                                            4
  138 000000F8 00000000        DCD              TIM8_CC_IRQHandler ; TIM8 Captu
                                                            re Compare         
                                                                               
                                                                   
  139 000000FC 00000000        DCD              DMA1_Stream7_IRQHandler ; DMA1 
                                                            Stream7            
                                                                               
                                                                        
  140 00000100 00000000        DCD              FMC_IRQHandler ; FMC           
                                                                               
                                                                           
  141 00000104 00000000        DCD              SDIO_IRQHandler ; SDIO         
                                                                               
                                                                            
  142 00000108 00000000        DCD              TIM5_IRQHandler ; TIM5         
                                                                               
                                                                            
  143 0000010C 00000000        DCD              SPI3_IRQHandler ; SPI3         
                                                                               
                                                                            
  144 00000110 00000000        DCD              UART4_IRQHandler ; UART4       
                                                                               
                                                                             
  145 00000114 00000000        DCD              UART5_IRQHandler ; UART5       
                                                                               
                                                                             
  146 00000118 00000000        DCD              TIM6_DAC_IRQHandler ; TIM6 and 
                                                            DAC1&2 underrun err
                                                            ors                
                                                               
  147 0000011C 00000000        DCD              TIM7_IRQHandler ; TIM7         
                                                                      
  148 00000120 00000000        DCD              DMA2_Stream0_IRQHandler ; DMA2 
                                                            Stream 0           
                                                                               
                                                                 
  149 00000124 00000000        DCD              DMA2_Stream1_IRQHandler ; DMA2 
                                                            Stream 1           
                                                                               
                                                                 
  150 00000128 00000000        DCD              DMA2_Stream2_IRQHandler ; DMA2 
                                                            Stream 2           
                                                                               
                                                                 
  151 0000012C 00000000        DCD              DMA2_Stream3_IRQHandler ; DMA2 
                                                            Stream 3           
                                                                               



ARM Macro Assembler    Page 7 


                                                                 
  152 00000130 00000000        DCD              DMA2_Stream4_IRQHandler ; DMA2 
                                                            Stream 4           
                                                                               
                                                                 
  153 00000134 00000000        DCD              ETH_IRQHandler ; Ethernet      
                                                                               
                                                                           
  154 00000138 00000000        DCD              ETH_WKUP_IRQHandler ; Ethernet 
                                                            Wakeup through EXTI
                                                             line              
                                                                    
  155 0000013C 00000000        DCD              CAN2_TX_IRQHandler ; CAN2 TX   
                                                                               
                                                                               
                                                                   
  156 00000140 00000000        DCD              CAN2_RX0_IRQHandler ; CAN2 RX0 
                                                                               
                                                                               
                                                                    
  157 00000144 00000000        DCD              CAN2_RX1_IRQHandler ; CAN2 RX1 
                                                                               
                                                                               
                                                                    
  158 00000148 00000000        DCD              CAN2_SCE_IRQHandler ; CAN2 SCE 
                                                                               
                                                                               
                                                                    
  159 0000014C 00000000        DCD              OTG_FS_IRQHandler ; USB OTG FS 
                                                                               
                                                                              
  160 00000150 00000000        DCD              DMA2_Stream5_IRQHandler ; DMA2 
                                                            Stream 5           
                                                                               
                                                                 
  161 00000154 00000000        DCD              DMA2_Stream6_IRQHandler ; DMA2 
                                                            Stream 6           
                                                                               
                                                                 
  162 00000158 00000000        DCD              DMA2_Stream7_IRQHandler ; DMA2 
                                                            Stream 7           
                                                                               
                                                                 
  163 0000015C 00000000        DCD              USART6_IRQHandler ; USART6     
                                                                               
                                                                               
                                                            
  164 00000160 00000000        DCD              I2C3_EV_IRQHandler ; I2C3 event
                                                                               
                                                                               
                                                                   
  165 00000164 00000000        DCD              I2C3_ER_IRQHandler ; I2C3 error
                                                                               
                                                                               
                                                                   
  166 00000168 00000000        DCD              OTG_HS_EP1_OUT_IRQHandler ; USB
                                                             OTG HS End Point 1
                                                             Out               
                                                                   



ARM Macro Assembler    Page 8 


  167 0000016C 00000000        DCD              OTG_HS_EP1_IN_IRQHandler ; USB 
                                                            OTG HS End Point 1 
                                                            In                 
                                                                  
  168 00000170 00000000        DCD              OTG_HS_WKUP_IRQHandler ; USB OT
                                                            G HS Wakeup through
                                                             EXTI              
                                                                       
  169 00000174 00000000        DCD              OTG_HS_IRQHandler ; USB OTG HS 
                                                                               
                                                                              
  170 00000178 00000000        DCD              DCMI_IRQHandler ; DCMI  
  171 0000017C 00000000        DCD              0           ; Reserved         
                                                                               
                                                                  
  172 00000180 00000000        DCD              HASH_RNG_IRQHandler 
                                                            ; Hash and Rng
  173 00000184 00000000        DCD              FPU_IRQHandler ; FPU
  174 00000188         
  175 00000188         
  176 00000188         __Vectors_End
  177 00000188         
  178 00000188 00000188 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  179 00000188         
  180 00000188                 AREA             |.text|, CODE, READONLY
  181 00000000         
  182 00000000         ; Reset handler
  183 00000000         Reset_Handler
                               PROC
  184 00000000                 EXPORT           Reset_Handler             [WEAK
]
  185 00000000                 IMPORT           SystemInit
  186 00000000                 IMPORT           __main
  187 00000000         
  188 00000000 4806            LDR              R0, =SystemInit
  189 00000002 4780            BLX              R0
  190 00000004 4806            LDR              R0, =__main
  191 00000006 4700            BX               R0
  192 00000008                 ENDP
  193 00000008         
  194 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  195 00000008         
  196 00000008         NMI_Handler
                               PROC
  197 00000008                 EXPORT           NMI_Handler                [WEA
K]
  198 00000008 E7FE            B                .
  199 0000000A                 ENDP
  201 0000000A         HardFault_Handler
                               PROC
  202 0000000A                 EXPORT           HardFault_Handler          [WEA
K]
  203 0000000A E7FE            B                .
  204 0000000C                 ENDP
  206 0000000C         MemManage_Handler
                               PROC



ARM Macro Assembler    Page 9 


  207 0000000C                 EXPORT           MemManage_Handler          [WEA
K]
  208 0000000C E7FE            B                .
  209 0000000E                 ENDP
  211 0000000E         BusFault_Handler
                               PROC
  212 0000000E                 EXPORT           BusFault_Handler           [WEA
K]
  213 0000000E E7FE            B                .
  214 00000010                 ENDP
  216 00000010         UsageFault_Handler
                               PROC
  217 00000010                 EXPORT           UsageFault_Handler         [WEA
K]
  218 00000010 E7FE            B                .
  219 00000012                 ENDP
  220 00000012         SVC_Handler
                               PROC
  221 00000012                 EXPORT           SVC_Handler                [WEA
K]
  222 00000012 E7FE            B                .
  223 00000014                 ENDP
  225 00000014         DebugMon_Handler
                               PROC
  226 00000014                 EXPORT           DebugMon_Handler           [WEA
K]
  227 00000014 E7FE            B                .
  228 00000016                 ENDP
  229 00000016         PendSV_Handler
                               PROC
  230 00000016                 EXPORT           PendSV_Handler             [WEA
K]
  231 00000016 E7FE            B                .
  232 00000018                 ENDP
  233 00000018         SysTick_Handler
                               PROC
  234 00000018                 EXPORT           SysTick_Handler            [WEA
K]
  235 00000018 E7FE            B                .
  236 0000001A                 ENDP
  237 0000001A         
  238 0000001A         Default_Handler
                               PROC
  239 0000001A         
  240 0000001A                 EXPORT           WWDG_IRQHandler                
   [WEAK]
  241 0000001A                 EXPORT           PVD_IRQHandler                 
   [WEAK]
  242 0000001A                 EXPORT           TAMP_STAMP_IRQHandler          
   [WEAK]
  243 0000001A                 EXPORT           RTC_WKUP_IRQHandler            
   [WEAK]
  244 0000001A                 EXPORT           FLASH_IRQHandler               
   [WEAK]
  245 0000001A                 EXPORT           RCC_IRQHandler                 
   [WEAK]
  246 0000001A                 EXPORT           EXTI0_IRQHandler               
   [WEAK]
  247 0000001A                 EXPORT           EXTI1_IRQHandler               



ARM Macro Assembler    Page 10 


   [WEAK]
  248 0000001A                 EXPORT           EXTI2_IRQHandler               
   [WEAK]
  249 0000001A                 EXPORT           EXTI3_IRQHandler               
   [WEAK]
  250 0000001A                 EXPORT           EXTI4_IRQHandler               
   [WEAK]
  251 0000001A                 EXPORT           DMA1_Stream0_IRQHandler        
   [WEAK]
  252 0000001A                 EXPORT           DMA1_Stream1_IRQHandler        
   [WEAK]
  253 0000001A                 EXPORT           DMA1_Stream2_IRQHandler        
   [WEAK]
  254 0000001A                 EXPORT           DMA1_Stream3_IRQHandler        
   [WEAK]
  255 0000001A                 EXPORT           DMA1_Stream4_IRQHandler        
   [WEAK]
  256 0000001A                 EXPORT           DMA1_Stream5_IRQHandler        
   [WEAK]
  257 0000001A                 EXPORT           DMA1_Stream6_IRQHandler        
   [WEAK]
  258 0000001A                 EXPORT           ADC_IRQHandler                 
   [WEAK]
  259 0000001A                 EXPORT           CAN1_TX_IRQHandler             
   [WEAK]
  260 0000001A                 EXPORT           CAN1_RX0_IRQHandler            
   [WEAK]
  261 0000001A                 EXPORT           CAN1_RX1_IRQHandler            
   [WEAK]
  262 0000001A                 EXPORT           CAN1_SCE_IRQHandler            
   [WEAK]
  263 0000001A                 EXPORT           EXTI9_5_IRQHandler             
   [WEAK]
  264 0000001A                 EXPORT           TIM1_BRK_TIM9_IRQHandler       
   [WEAK]
  265 0000001A                 EXPORT           TIM1_UP_TIM10_IRQHandler       
   [WEAK]
  266 0000001A                 EXPORT           TIM1_TRG_COM_TIM11_IRQHandler  
   [WEAK]
  267 0000001A                 EXPORT           TIM1_CC_IRQHandler             
   [WEAK]
  268 0000001A                 EXPORT           TIM2_IRQHandler                
   [WEAK]
  269 0000001A                 EXPORT           TIM3_IRQHandler                
   [WEAK]
  270 0000001A                 EXPORT           TIM4_IRQHandler                
   [WEAK]
  271 0000001A                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  272 0000001A                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  273 0000001A                 EXPORT           I2C2_EV_IRQHandler             
   [WEAK]
  274 0000001A                 EXPORT           I2C2_ER_IRQHandler             
   [WEAK]
  275 0000001A                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  276 0000001A                 EXPORT           SPI2_IRQHandler                
   [WEAK]



ARM Macro Assembler    Page 11 


  277 0000001A                 EXPORT           USART1_IRQHandler              
   [WEAK]
  278 0000001A                 EXPORT           USART2_IRQHandler              
   [WEAK]
  279 0000001A                 EXPORT           USART3_IRQHandler              
   [WEAK]
  280 0000001A                 EXPORT           EXTI15_10_IRQHandler           
   [WEAK]
  281 0000001A                 EXPORT           RTC_Alarm_IRQHandler           
   [WEAK]
  282 0000001A                 EXPORT           OTG_FS_WKUP_IRQHandler         
   [WEAK]
  283 0000001A                 EXPORT           TIM8_BRK_TIM12_IRQHandler      
   [WEAK]
  284 0000001A                 EXPORT           TIM8_UP_TIM13_IRQHandler       
   [WEAK]
  285 0000001A                 EXPORT           TIM8_TRG_COM_TIM14_IRQHandler  
   [WEAK]
  286 0000001A                 EXPORT           TIM8_CC_IRQHandler             
   [WEAK]
  287 0000001A                 EXPORT           DMA1_Stream7_IRQHandler        
   [WEAK]
  288 0000001A                 EXPORT           FMC_IRQHandler                 
   [WEAK]
  289 0000001A                 EXPORT           SDIO_IRQHandler                
   [WEAK]
  290 0000001A                 EXPORT           TIM5_IRQHandler                
   [WEAK]
  291 0000001A                 EXPORT           SPI3_IRQHandler                
   [WEAK]
  292 0000001A                 EXPORT           UART4_IRQHandler               
   [WEAK]
  293 0000001A                 EXPORT           UART5_IRQHandler               
   [WEAK]
  294 0000001A                 EXPORT           TIM6_DAC_IRQHandler            
   [WEAK]
  295 0000001A                 EXPORT           TIM7_IRQHandler                
   [WEAK]
  296 0000001A                 EXPORT           DMA2_Stream0_IRQHandler        
   [WEAK]
  297 0000001A                 EXPORT           DMA2_Stream1_IRQHandler        
   [WEAK]
  298 0000001A                 EXPORT           DMA2_Stream2_IRQHandler        
   [WEAK]
  299 0000001A                 EXPORT           DMA2_Stream3_IRQHandler        
   [WEAK]
  300 0000001A                 EXPORT           DMA2_Stream4_IRQHandler        
   [WEAK]
  301 0000001A                 EXPORT           ETH_IRQHandler                 
   [WEAK]
  302 0000001A                 EXPORT           ETH_WKUP_IRQHandler            
   [WEAK]
  303 0000001A                 EXPORT           CAN2_TX_IRQHandler             
   [WEAK]
  304 0000001A                 EXPORT           CAN2_RX0_IRQHandler            
   [WEAK]
  305 0000001A                 EXPORT           CAN2_RX1_IRQHandler            
   [WEAK]
  306 0000001A                 EXPORT           CAN2_SCE_IRQHandler            



ARM Macro Assembler    Page 12 


   [WEAK]
  307 0000001A                 EXPORT           OTG_FS_IRQHandler              
   [WEAK]
  308 0000001A                 EXPORT           DMA2_Stream5_IRQHandler        
   [WEAK]
  309 0000001A                 EXPORT           DMA2_Stream6_IRQHandler        
   [WEAK]
  310 0000001A                 EXPORT           DMA2_Stream7_IRQHandler        
   [WEAK]
  311 0000001A                 EXPORT           USART6_IRQHandler              
   [WEAK]
  312 0000001A                 EXPORT           I2C3_EV_IRQHandler             
   [WEAK]
  313 0000001A                 EXPORT           I2C3_ER_IRQHandler             
   [WEAK]
  314 0000001A                 EXPORT           OTG_HS_EP1_OUT_IRQHandler      
   [WEAK]
  315 0000001A                 EXPORT           OTG_HS_EP1_IN_IRQHandler       
   [WEAK]
  316 0000001A                 EXPORT           OTG_HS_WKUP_IRQHandler         
   [WEAK]
  317 0000001A                 EXPORT           OTG_HS_IRQHandler              
   [WEAK]
  318 0000001A                 EXPORT           DCMI_IRQHandler                
   [WEAK]
  319 0000001A                 EXPORT           HASH_RNG_IRQHandler            
   [WEAK]
  320 0000001A                 EXPORT           FPU_IRQHandler                 
   [WEAK]
  321 0000001A         
  322 0000001A         WWDG_IRQHandler
  323 0000001A         PVD_IRQHandler
  324 0000001A         TAMP_STAMP_IRQHandler
  325 0000001A         RTC_WKUP_IRQHandler
  326 0000001A         FLASH_IRQHandler
  327 0000001A         RCC_IRQHandler
  328 0000001A         EXTI0_IRQHandler
  329 0000001A         EXTI1_IRQHandler
  330 0000001A         EXTI2_IRQHandler
  331 0000001A         EXTI3_IRQHandler
  332 0000001A         EXTI4_IRQHandler
  333 0000001A         DMA1_Stream0_IRQHandler
  334 0000001A         DMA1_Stream1_IRQHandler
  335 0000001A         DMA1_Stream2_IRQHandler
  336 0000001A         DMA1_Stream3_IRQHandler
  337 0000001A         DMA1_Stream4_IRQHandler
  338 0000001A         DMA1_Stream5_IRQHandler
  339 0000001A         DMA1_Stream6_IRQHandler
  340 0000001A         ADC_IRQHandler
  341 0000001A         CAN1_TX_IRQHandler
  342 0000001A         CAN1_RX0_IRQHandler
  343 0000001A         CAN1_RX1_IRQHandler
  344 0000001A         CAN1_SCE_IRQHandler
  345 0000001A         EXTI9_5_IRQHandler
  346 0000001A         TIM1_BRK_TIM9_IRQHandler
  347 0000001A         TIM1_UP_TIM10_IRQHandler
  348 0000001A         TIM1_TRG_COM_TIM11_IRQHandler
  349 0000001A         TIM1_CC_IRQHandler
  350 0000001A         TIM2_IRQHandler



ARM Macro Assembler    Page 13 


  351 0000001A         TIM3_IRQHandler
  352 0000001A         TIM4_IRQHandler
  353 0000001A         I2C1_EV_IRQHandler
  354 0000001A         I2C1_ER_IRQHandler
  355 0000001A         I2C2_EV_IRQHandler
  356 0000001A         I2C2_ER_IRQHandler
  357 0000001A         SPI1_IRQHandler
  358 0000001A         SPI2_IRQHandler
  359 0000001A         USART1_IRQHandler
  360 0000001A         USART2_IRQHandler
  361 0000001A         USART3_IRQHandler
  362 0000001A         EXTI15_10_IRQHandler
  363 0000001A         RTC_Alarm_IRQHandler
  364 0000001A         OTG_FS_WKUP_IRQHandler
  365 0000001A         TIM8_BRK_TIM12_IRQHandler
  366 0000001A         TIM8_UP_TIM13_IRQHandler
  367 0000001A         TIM8_TRG_COM_TIM14_IRQHandler
  368 0000001A         TIM8_CC_IRQHandler
  369 0000001A         DMA1_Stream7_IRQHandler
  370 0000001A         FMC_IRQHandler
  371 0000001A         SDIO_IRQHandler
  372 0000001A         TIM5_IRQHandler
  373 0000001A         SPI3_IRQHandler
  374 0000001A         UART4_IRQHandler
  375 0000001A         UART5_IRQHandler
  376 0000001A         TIM6_DAC_IRQHandler
  377 0000001A         TIM7_IRQHandler
  378 0000001A         DMA2_Stream0_IRQHandler
  379 0000001A         DMA2_Stream1_IRQHandler
  380 0000001A         DMA2_Stream2_IRQHandler
  381 0000001A         DMA2_Stream3_IRQHandler
  382 0000001A         DMA2_Stream4_IRQHandler
  383 0000001A         ETH_IRQHandler
  384 0000001A         ETH_WKUP_IRQHandler
  385 0000001A         CAN2_TX_IRQHandler
  386 0000001A         CAN2_RX0_IRQHandler
  387 0000001A         CAN2_RX1_IRQHandler
  388 0000001A         CAN2_SCE_IRQHandler
  389 0000001A         OTG_FS_IRQHandler
  390 0000001A         DMA2_Stream5_IRQHandler
  391 0000001A         DMA2_Stream6_IRQHandler
  392 0000001A         DMA2_Stream7_IRQHandler
  393 0000001A         USART6_IRQHandler
  394 0000001A         I2C3_EV_IRQHandler
  395 0000001A         I2C3_ER_IRQHandler
  396 0000001A         OTG_HS_EP1_OUT_IRQHandler
  397 0000001A         OTG_HS_EP1_IN_IRQHandler
  398 0000001A         OTG_HS_WKUP_IRQHandler
  399 0000001A         OTG_HS_IRQHandler
  400 0000001A         DCMI_IRQHandler
  401 0000001A         HASH_RNG_IRQHandler
  402 0000001A         FPU_IRQHandler
  403 0000001A         
  404 0000001A E7FE            B                .
  405 0000001C         
  406 0000001C                 ENDP
  407 0000001C         
  408 0000001C                 ALIGN
  409 0000001C         



ARM Macro Assembler    Page 14 


  410 0000001C         ;*******************************************************
                       ************************
  411 0000001C         ; User Stack and Heap initialization
  412 0000001C         ;*******************************************************
                       ************************
  413 0000001C                 IF               :DEF:__MICROLIB
  414 0000001C         
  415 0000001C                 EXPORT           __initial_sp
  416 0000001C                 EXPORT           __heap_base
  417 0000001C                 EXPORT           __heap_limit
  418 0000001C         
  419 0000001C                 ELSE
  434                          ENDIF
  435 0000001C         
  436 0000001C                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=stm32f407_uart_cmd\startup_stm32f407xx.d -ostm32f407_uart_cm
d\startup_stm32f407xx.o -I.\RTE\_STM32F407_UART_CMD -IC:\Users\<USER>\AppData\Lo
cal\Arm\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include -IC:\Users\<USER>\AppData\Local
\Arm\Packs\Keil\STM32F4xx_DFP\2.7.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include -
-predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 536" --prede
fine="_RTE_ SETA 1" --predefine="STM32F407xx SETA 1" --predefine="_RTE_ SETA 1"
 --list=startup_stm32f407xx.lst startup_stm32f407xx.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 48 in file startup_stm32f407xx.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 49 in file startup_stm32f407xx.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00001000

Symbol: __initial_sp
   Definitions
      At line 50 in file startup_stm32f407xx.s
   Uses
      At line 74 in file startup_stm32f407xx.s
      At line 415 in file startup_stm32f407xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 59 in file startup_stm32f407xx.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 61 in file startup_stm32f407xx.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 60 in file startup_stm32f407xx.s
   Uses
      At line 416 in file startup_stm32f407xx.s
Comment: __heap_base used once
__heap_limit 00000400

Symbol: __heap_limit
   Definitions
      At line 62 in file startup_stm32f407xx.s
   Uses
      At line 417 in file startup_stm32f407xx.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 69 in file startup_stm32f407xx.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 74 in file startup_stm32f407xx.s
   Uses
      At line 70 in file startup_stm32f407xx.s
      At line 178 in file startup_stm32f407xx.s

__Vectors_End 00000188

Symbol: __Vectors_End
   Definitions
      At line 176 in file startup_stm32f407xx.s
   Uses
      At line 71 in file startup_stm32f407xx.s
      At line 178 in file startup_stm32f407xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 180 in file startup_stm32f407xx.s
   Uses
      None
Comment: .text unused
ADC_IRQHandler 0000001A

Symbol: ADC_IRQHandler
   Definitions
      At line 340 in file startup_stm32f407xx.s
   Uses
      At line 110 in file startup_stm32f407xx.s
      At line 258 in file startup_stm32f407xx.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 211 in file startup_stm32f407xx.s
   Uses
      At line 79 in file startup_stm32f407xx.s
      At line 212 in file startup_stm32f407xx.s

CAN1_RX0_IRQHandler 0000001A

Symbol: CAN1_RX0_IRQHandler
   Definitions
      At line 342 in file startup_stm32f407xx.s
   Uses
      At line 112 in file startup_stm32f407xx.s
      At line 260 in file startup_stm32f407xx.s

CAN1_RX1_IRQHandler 0000001A

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 343 in file startup_stm32f407xx.s
   Uses
      At line 113 in file startup_stm32f407xx.s
      At line 261 in file startup_stm32f407xx.s

CAN1_SCE_IRQHandler 0000001A

Symbol: CAN1_SCE_IRQHandler
   Definitions
      At line 344 in file startup_stm32f407xx.s
   Uses
      At line 114 in file startup_stm32f407xx.s
      At line 262 in file startup_stm32f407xx.s

CAN1_TX_IRQHandler 0000001A

Symbol: CAN1_TX_IRQHandler
   Definitions
      At line 341 in file startup_stm32f407xx.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 111 in file startup_stm32f407xx.s
      At line 259 in file startup_stm32f407xx.s

CAN2_RX0_IRQHandler 0000001A

Symbol: CAN2_RX0_IRQHandler
   Definitions
      At line 386 in file startup_stm32f407xx.s
   Uses
      At line 156 in file startup_stm32f407xx.s
      At line 304 in file startup_stm32f407xx.s

CAN2_RX1_IRQHandler 0000001A

Symbol: CAN2_RX1_IRQHandler
   Definitions
      At line 387 in file startup_stm32f407xx.s
   Uses
      At line 157 in file startup_stm32f407xx.s
      At line 305 in file startup_stm32f407xx.s

CAN2_SCE_IRQHandler 0000001A

Symbol: CAN2_SCE_IRQHandler
   Definitions
      At line 388 in file startup_stm32f407xx.s
   Uses
      At line 158 in file startup_stm32f407xx.s
      At line 306 in file startup_stm32f407xx.s

CAN2_TX_IRQHandler 0000001A

Symbol: CAN2_TX_IRQHandler
   Definitions
      At line 385 in file startup_stm32f407xx.s
   Uses
      At line 155 in file startup_stm32f407xx.s
      At line 303 in file startup_stm32f407xx.s

DCMI_IRQHandler 0000001A

Symbol: DCMI_IRQHandler
   Definitions
      At line 400 in file startup_stm32f407xx.s
   Uses
      At line 170 in file startup_stm32f407xx.s
      At line 318 in file startup_stm32f407xx.s

DMA1_Stream0_IRQHandler 0000001A

Symbol: DMA1_Stream0_IRQHandler
   Definitions
      At line 333 in file startup_stm32f407xx.s
   Uses
      At line 103 in file startup_stm32f407xx.s
      At line 251 in file startup_stm32f407xx.s

DMA1_Stream1_IRQHandler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: DMA1_Stream1_IRQHandler
   Definitions
      At line 334 in file startup_stm32f407xx.s
   Uses
      At line 104 in file startup_stm32f407xx.s
      At line 252 in file startup_stm32f407xx.s

DMA1_Stream2_IRQHandler 0000001A

Symbol: DMA1_Stream2_IRQHandler
   Definitions
      At line 335 in file startup_stm32f407xx.s
   Uses
      At line 105 in file startup_stm32f407xx.s
      At line 253 in file startup_stm32f407xx.s

DMA1_Stream3_IRQHandler 0000001A

Symbol: DMA1_Stream3_IRQHandler
   Definitions
      At line 336 in file startup_stm32f407xx.s
   Uses
      At line 106 in file startup_stm32f407xx.s
      At line 254 in file startup_stm32f407xx.s

DMA1_Stream4_IRQHandler 0000001A

Symbol: DMA1_Stream4_IRQHandler
   Definitions
      At line 337 in file startup_stm32f407xx.s
   Uses
      At line 107 in file startup_stm32f407xx.s
      At line 255 in file startup_stm32f407xx.s

DMA1_Stream5_IRQHandler 0000001A

Symbol: DMA1_Stream5_IRQHandler
   Definitions
      At line 338 in file startup_stm32f407xx.s
   Uses
      At line 108 in file startup_stm32f407xx.s
      At line 256 in file startup_stm32f407xx.s

DMA1_Stream6_IRQHandler 0000001A

Symbol: DMA1_Stream6_IRQHandler
   Definitions
      At line 339 in file startup_stm32f407xx.s
   Uses
      At line 109 in file startup_stm32f407xx.s
      At line 257 in file startup_stm32f407xx.s

DMA1_Stream7_IRQHandler 0000001A

Symbol: DMA1_Stream7_IRQHandler
   Definitions
      At line 369 in file startup_stm32f407xx.s
   Uses
      At line 139 in file startup_stm32f407xx.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 287 in file startup_stm32f407xx.s

DMA2_Stream0_IRQHandler 0000001A

Symbol: DMA2_Stream0_IRQHandler
   Definitions
      At line 378 in file startup_stm32f407xx.s
   Uses
      At line 148 in file startup_stm32f407xx.s
      At line 296 in file startup_stm32f407xx.s

DMA2_Stream1_IRQHandler 0000001A

Symbol: DMA2_Stream1_IRQHandler
   Definitions
      At line 379 in file startup_stm32f407xx.s
   Uses
      At line 149 in file startup_stm32f407xx.s
      At line 297 in file startup_stm32f407xx.s

DMA2_Stream2_IRQHandler 0000001A

Symbol: DMA2_Stream2_IRQHandler
   Definitions
      At line 380 in file startup_stm32f407xx.s
   Uses
      At line 150 in file startup_stm32f407xx.s
      At line 298 in file startup_stm32f407xx.s

DMA2_Stream3_IRQHandler 0000001A

Symbol: DMA2_Stream3_IRQHandler
   Definitions
      At line 381 in file startup_stm32f407xx.s
   Uses
      At line 151 in file startup_stm32f407xx.s
      At line 299 in file startup_stm32f407xx.s

DMA2_Stream4_IRQHandler 0000001A

Symbol: DMA2_Stream4_IRQHandler
   Definitions
      At line 382 in file startup_stm32f407xx.s
   Uses
      At line 152 in file startup_stm32f407xx.s
      At line 300 in file startup_stm32f407xx.s

DMA2_Stream5_IRQHandler 0000001A

Symbol: DMA2_Stream5_IRQHandler
   Definitions
      At line 390 in file startup_stm32f407xx.s
   Uses
      At line 160 in file startup_stm32f407xx.s
      At line 308 in file startup_stm32f407xx.s

DMA2_Stream6_IRQHandler 0000001A

Symbol: DMA2_Stream6_IRQHandler



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 391 in file startup_stm32f407xx.s
   Uses
      At line 161 in file startup_stm32f407xx.s
      At line 309 in file startup_stm32f407xx.s

DMA2_Stream7_IRQHandler 0000001A

Symbol: DMA2_Stream7_IRQHandler
   Definitions
      At line 392 in file startup_stm32f407xx.s
   Uses
      At line 162 in file startup_stm32f407xx.s
      At line 310 in file startup_stm32f407xx.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 225 in file startup_stm32f407xx.s
   Uses
      At line 86 in file startup_stm32f407xx.s
      At line 226 in file startup_stm32f407xx.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 238 in file startup_stm32f407xx.s
   Uses
      None
Comment: Default_Handler unused
ETH_IRQHandler 0000001A

Symbol: ETH_IRQHandler
   Definitions
      At line 383 in file startup_stm32f407xx.s
   Uses
      At line 153 in file startup_stm32f407xx.s
      At line 301 in file startup_stm32f407xx.s

ETH_WKUP_IRQHandler 0000001A

Symbol: ETH_WKUP_IRQHandler
   Definitions
      At line 384 in file startup_stm32f407xx.s
   Uses
      At line 154 in file startup_stm32f407xx.s
      At line 302 in file startup_stm32f407xx.s

EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 328 in file startup_stm32f407xx.s
   Uses
      At line 98 in file startup_stm32f407xx.s
      At line 246 in file startup_stm32f407xx.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

EXTI15_10_IRQHandler 0000001A

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 362 in file startup_stm32f407xx.s
   Uses
      At line 132 in file startup_stm32f407xx.s
      At line 280 in file startup_stm32f407xx.s

EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions
      At line 329 in file startup_stm32f407xx.s
   Uses
      At line 99 in file startup_stm32f407xx.s
      At line 247 in file startup_stm32f407xx.s

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 330 in file startup_stm32f407xx.s
   Uses
      At line 100 in file startup_stm32f407xx.s
      At line 248 in file startup_stm32f407xx.s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions
      At line 331 in file startup_stm32f407xx.s
   Uses
      At line 101 in file startup_stm32f407xx.s
      At line 249 in file startup_stm32f407xx.s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 332 in file startup_stm32f407xx.s
   Uses
      At line 102 in file startup_stm32f407xx.s
      At line 250 in file startup_stm32f407xx.s

EXTI9_5_IRQHandler 0000001A

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 345 in file startup_stm32f407xx.s
   Uses
      At line 115 in file startup_stm32f407xx.s
      At line 263 in file startup_stm32f407xx.s

FLASH_IRQHandler 0000001A

Symbol: FLASH_IRQHandler
   Definitions
      At line 326 in file startup_stm32f407xx.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 96 in file startup_stm32f407xx.s
      At line 244 in file startup_stm32f407xx.s

FMC_IRQHandler 0000001A

Symbol: FMC_IRQHandler
   Definitions
      At line 370 in file startup_stm32f407xx.s
   Uses
      At line 140 in file startup_stm32f407xx.s
      At line 288 in file startup_stm32f407xx.s

FPU_IRQHandler 0000001A

Symbol: FPU_IRQHandler
   Definitions
      At line 402 in file startup_stm32f407xx.s
   Uses
      At line 173 in file startup_stm32f407xx.s
      At line 320 in file startup_stm32f407xx.s

HASH_RNG_IRQHandler 0000001A

Symbol: HASH_RNG_IRQHandler
   Definitions
      At line 401 in file startup_stm32f407xx.s
   Uses
      At line 172 in file startup_stm32f407xx.s
      At line 319 in file startup_stm32f407xx.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 201 in file startup_stm32f407xx.s
   Uses
      At line 77 in file startup_stm32f407xx.s
      At line 202 in file startup_stm32f407xx.s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 354 in file startup_stm32f407xx.s
   Uses
      At line 124 in file startup_stm32f407xx.s
      At line 272 in file startup_stm32f407xx.s

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 353 in file startup_stm32f407xx.s
   Uses
      At line 123 in file startup_stm32f407xx.s
      At line 271 in file startup_stm32f407xx.s

I2C2_ER_IRQHandler 0000001A



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 356 in file startup_stm32f407xx.s
   Uses
      At line 126 in file startup_stm32f407xx.s
      At line 274 in file startup_stm32f407xx.s

I2C2_EV_IRQHandler 0000001A

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 355 in file startup_stm32f407xx.s
   Uses
      At line 125 in file startup_stm32f407xx.s
      At line 273 in file startup_stm32f407xx.s

I2C3_ER_IRQHandler 0000001A

Symbol: I2C3_ER_IRQHandler
   Definitions
      At line 395 in file startup_stm32f407xx.s
   Uses
      At line 165 in file startup_stm32f407xx.s
      At line 313 in file startup_stm32f407xx.s

I2C3_EV_IRQHandler 0000001A

Symbol: I2C3_EV_IRQHandler
   Definitions
      At line 394 in file startup_stm32f407xx.s
   Uses
      At line 164 in file startup_stm32f407xx.s
      At line 312 in file startup_stm32f407xx.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 206 in file startup_stm32f407xx.s
   Uses
      At line 78 in file startup_stm32f407xx.s
      At line 207 in file startup_stm32f407xx.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 196 in file startup_stm32f407xx.s
   Uses
      At line 76 in file startup_stm32f407xx.s
      At line 197 in file startup_stm32f407xx.s

OTG_FS_IRQHandler 0000001A

Symbol: OTG_FS_IRQHandler
   Definitions
      At line 389 in file startup_stm32f407xx.s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 159 in file startup_stm32f407xx.s
      At line 307 in file startup_stm32f407xx.s

OTG_FS_WKUP_IRQHandler 0000001A

Symbol: OTG_FS_WKUP_IRQHandler
   Definitions
      At line 364 in file startup_stm32f407xx.s
   Uses
      At line 134 in file startup_stm32f407xx.s
      At line 282 in file startup_stm32f407xx.s

OTG_HS_EP1_IN_IRQHandler 0000001A

Symbol: OTG_HS_EP1_IN_IRQHandler
   Definitions
      At line 397 in file startup_stm32f407xx.s
   Uses
      At line 167 in file startup_stm32f407xx.s
      At line 315 in file startup_stm32f407xx.s

OTG_HS_EP1_OUT_IRQHandler 0000001A

Symbol: OTG_HS_EP1_OUT_IRQHandler
   Definitions
      At line 396 in file startup_stm32f407xx.s
   Uses
      At line 166 in file startup_stm32f407xx.s
      At line 314 in file startup_stm32f407xx.s

OTG_HS_IRQHandler 0000001A

Symbol: OTG_HS_IRQHandler
   Definitions
      At line 399 in file startup_stm32f407xx.s
   Uses
      At line 169 in file startup_stm32f407xx.s
      At line 317 in file startup_stm32f407xx.s

OTG_HS_WKUP_IRQHandler 0000001A

Symbol: OTG_HS_WKUP_IRQHandler
   Definitions
      At line 398 in file startup_stm32f407xx.s
   Uses
      At line 168 in file startup_stm32f407xx.s
      At line 316 in file startup_stm32f407xx.s

PVD_IRQHandler 0000001A

Symbol: PVD_IRQHandler
   Definitions
      At line 323 in file startup_stm32f407xx.s
   Uses
      At line 93 in file startup_stm32f407xx.s
      At line 241 in file startup_stm32f407xx.s

PendSV_Handler 00000016




ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

Symbol: PendSV_Handler
   Definitions
      At line 229 in file startup_stm32f407xx.s
   Uses
      At line 88 in file startup_stm32f407xx.s
      At line 230 in file startup_stm32f407xx.s

RCC_IRQHandler 0000001A

Symbol: RCC_IRQHandler
   Definitions
      At line 327 in file startup_stm32f407xx.s
   Uses
      At line 97 in file startup_stm32f407xx.s
      At line 245 in file startup_stm32f407xx.s

RTC_Alarm_IRQHandler 0000001A

Symbol: RTC_Alarm_IRQHandler
   Definitions
      At line 363 in file startup_stm32f407xx.s
   Uses
      At line 133 in file startup_stm32f407xx.s
      At line 281 in file startup_stm32f407xx.s

RTC_WKUP_IRQHandler 0000001A

Symbol: RTC_WKUP_IRQHandler
   Definitions
      At line 325 in file startup_stm32f407xx.s
   Uses
      At line 95 in file startup_stm32f407xx.s
      At line 243 in file startup_stm32f407xx.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 183 in file startup_stm32f407xx.s
   Uses
      At line 75 in file startup_stm32f407xx.s
      At line 184 in file startup_stm32f407xx.s

SDIO_IRQHandler 0000001A

Symbol: SDIO_IRQHandler
   Definitions
      At line 371 in file startup_stm32f407xx.s
   Uses
      At line 141 in file startup_stm32f407xx.s
      At line 289 in file startup_stm32f407xx.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 357 in file startup_stm32f407xx.s
   Uses
      At line 127 in file startup_stm32f407xx.s



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 275 in file startup_stm32f407xx.s

SPI2_IRQHandler 0000001A

Symbol: SPI2_IRQHandler
   Definitions
      At line 358 in file startup_stm32f407xx.s
   Uses
      At line 128 in file startup_stm32f407xx.s
      At line 276 in file startup_stm32f407xx.s

SPI3_IRQHandler 0000001A

Symbol: SPI3_IRQHandler
   Definitions
      At line 373 in file startup_stm32f407xx.s
   Uses
      At line 143 in file startup_stm32f407xx.s
      At line 291 in file startup_stm32f407xx.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 220 in file startup_stm32f407xx.s
   Uses
      At line 85 in file startup_stm32f407xx.s
      At line 221 in file startup_stm32f407xx.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 233 in file startup_stm32f407xx.s
   Uses
      At line 89 in file startup_stm32f407xx.s
      At line 234 in file startup_stm32f407xx.s

TAMP_STAMP_IRQHandler 0000001A

Symbol: TAMP_STAMP_IRQHandler
   Definitions
      At line 324 in file startup_stm32f407xx.s
   Uses
      At line 94 in file startup_stm32f407xx.s
      At line 242 in file startup_stm32f407xx.s

TIM1_BRK_TIM9_IRQHandler 0000001A

Symbol: TIM1_BRK_TIM9_IRQHandler
   Definitions
      At line 346 in file startup_stm32f407xx.s
   Uses
      At line 116 in file startup_stm32f407xx.s
      At line 264 in file startup_stm32f407xx.s

TIM1_CC_IRQHandler 0000001A

Symbol: TIM1_CC_IRQHandler



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 349 in file startup_stm32f407xx.s
   Uses
      At line 119 in file startup_stm32f407xx.s
      At line 267 in file startup_stm32f407xx.s

TIM1_TRG_COM_TIM11_IRQHandler 0000001A

Symbol: TIM1_TRG_COM_TIM11_IRQHandler
   Definitions
      At line 348 in file startup_stm32f407xx.s
   Uses
      At line 118 in file startup_stm32f407xx.s
      At line 266 in file startup_stm32f407xx.s

TIM1_UP_TIM10_IRQHandler 0000001A

Symbol: TIM1_UP_TIM10_IRQHandler
   Definitions
      At line 347 in file startup_stm32f407xx.s
   Uses
      At line 117 in file startup_stm32f407xx.s
      At line 265 in file startup_stm32f407xx.s

TIM2_IRQHandler 0000001A

Symbol: TIM2_IRQHandler
   Definitions
      At line 350 in file startup_stm32f407xx.s
   Uses
      At line 120 in file startup_stm32f407xx.s
      At line 268 in file startup_stm32f407xx.s

TIM3_IRQHandler 0000001A

Symbol: TIM3_IRQHandler
   Definitions
      At line 351 in file startup_stm32f407xx.s
   Uses
      At line 121 in file startup_stm32f407xx.s
      At line 269 in file startup_stm32f407xx.s

TIM4_IRQHandler 0000001A

Symbol: TIM4_IRQHandler
   Definitions
      At line 352 in file startup_stm32f407xx.s
   Uses
      At line 122 in file startup_stm32f407xx.s
      At line 270 in file startup_stm32f407xx.s

TIM5_IRQHandler 0000001A

Symbol: TIM5_IRQHandler
   Definitions
      At line 372 in file startup_stm32f407xx.s
   Uses
      At line 142 in file startup_stm32f407xx.s
      At line 290 in file startup_stm32f407xx.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols


TIM6_DAC_IRQHandler 0000001A

Symbol: TIM6_DAC_IRQHandler
   Definitions
      At line 376 in file startup_stm32f407xx.s
   Uses
      At line 146 in file startup_stm32f407xx.s
      At line 294 in file startup_stm32f407xx.s

TIM7_IRQHandler 0000001A

Symbol: TIM7_IRQHandler
   Definitions
      At line 377 in file startup_stm32f407xx.s
   Uses
      At line 147 in file startup_stm32f407xx.s
      At line 295 in file startup_stm32f407xx.s

TIM8_BRK_TIM12_IRQHandler 0000001A

Symbol: TIM8_BRK_TIM12_IRQHandler
   Definitions
      At line 365 in file startup_stm32f407xx.s
   Uses
      At line 135 in file startup_stm32f407xx.s
      At line 283 in file startup_stm32f407xx.s

TIM8_CC_IRQHandler 0000001A

Symbol: TIM8_CC_IRQHandler
   Definitions
      At line 368 in file startup_stm32f407xx.s
   Uses
      At line 138 in file startup_stm32f407xx.s
      At line 286 in file startup_stm32f407xx.s

TIM8_TRG_COM_TIM14_IRQHandler 0000001A

Symbol: TIM8_TRG_COM_TIM14_IRQHandler
   Definitions
      At line 367 in file startup_stm32f407xx.s
   Uses
      At line 137 in file startup_stm32f407xx.s
      At line 285 in file startup_stm32f407xx.s

TIM8_UP_TIM13_IRQHandler 0000001A

Symbol: TIM8_UP_TIM13_IRQHandler
   Definitions
      At line 366 in file startup_stm32f407xx.s
   Uses
      At line 136 in file startup_stm32f407xx.s
      At line 284 in file startup_stm32f407xx.s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler
   Definitions



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

      At line 374 in file startup_stm32f407xx.s
   Uses
      At line 144 in file startup_stm32f407xx.s
      At line 292 in file startup_stm32f407xx.s

UART5_IRQHandler 0000001A

Symbol: UART5_IRQHandler
   Definitions
      At line 375 in file startup_stm32f407xx.s
   Uses
      At line 145 in file startup_stm32f407xx.s
      At line 293 in file startup_stm32f407xx.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 359 in file startup_stm32f407xx.s
   Uses
      At line 129 in file startup_stm32f407xx.s
      At line 277 in file startup_stm32f407xx.s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 360 in file startup_stm32f407xx.s
   Uses
      At line 130 in file startup_stm32f407xx.s
      At line 278 in file startup_stm32f407xx.s

USART3_IRQHandler 0000001A

Symbol: USART3_IRQHandler
   Definitions
      At line 361 in file startup_stm32f407xx.s
   Uses
      At line 131 in file startup_stm32f407xx.s
      At line 279 in file startup_stm32f407xx.s

USART6_IRQHandler 0000001A

Symbol: USART6_IRQHandler
   Definitions
      At line 393 in file startup_stm32f407xx.s
   Uses
      At line 163 in file startup_stm32f407xx.s
      At line 311 in file startup_stm32f407xx.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 216 in file startup_stm32f407xx.s
   Uses
      At line 80 in file startup_stm32f407xx.s
      At line 217 in file startup_stm32f407xx.s




ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols

WWDG_IRQHandler 0000001A

Symbol: WWDG_IRQHandler
   Definitions
      At line 322 in file startup_stm32f407xx.s
   Uses
      At line 92 in file startup_stm32f407xx.s
      At line 240 in file startup_stm32f407xx.s

93 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000400

Symbol: Heap_Size
   Definitions
      At line 57 in file startup_stm32f407xx.s
   Uses
      At line 61 in file startup_stm32f407xx.s
Comment: Heap_Size used once
Stack_Size 00001000

Symbol: Stack_Size
   Definitions
      At line 46 in file startup_stm32f407xx.s
   Uses
      At line 49 in file startup_stm32f407xx.s
Comment: Stack_Size used once
__Vectors_Size 00000188

Symbol: __Vectors_Size
   Definitions
      At line 178 in file startup_stm32f407xx.s
   Uses
      At line 72 in file startup_stm32f407xx.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 185 in file startup_stm32f407xx.s
   Uses
      At line 188 in file startup_stm32f407xx.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 186 in file startup_stm32f407xx.s
   Uses
      At line 190 in file startup_stm32f407xx.s
Comment: __main used once
2 symbols
445 symbols in table
