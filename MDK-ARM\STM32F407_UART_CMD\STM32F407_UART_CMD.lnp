--cpu=Cortex-M4.fp.sp
"stm32f407_uart_cmd\startup_stm32f407xx.o"
"stm32f407_uart_cmd\main.o"
"stm32f407_uart_cmd\gpio.o"
"stm32f407_uart_cmd\dma.o"
"stm32f407_uart_cmd\usart.o"
"stm32f407_uart_cmd\stm32f4xx_it.o"
"stm32f407_uart_cmd\stm32f4xx_hal_msp.o"
"stm32f407_uart_cmd\emm_v5.o"
"stm32f407_uart_cmd\stm32f4xx_hal_tim.o"
"stm32f407_uart_cmd\stm32f4xx_hal_tim_ex.o"
"stm32f407_uart_cmd\stm32f4xx_hal_uart.o"
"stm32f407_uart_cmd\stm32f4xx_hal_rcc.o"
"stm32f407_uart_cmd\stm32f4xx_hal_rcc_ex.o"
"stm32f407_uart_cmd\stm32f4xx_hal_flash.o"
"stm32f407_uart_cmd\stm32f4xx_hal_flash_ex.o"
"stm32f407_uart_cmd\stm32f4xx_hal_flash_ramfunc.o"
"stm32f407_uart_cmd\stm32f4xx_hal_gpio.o"
"stm32f407_uart_cmd\stm32f4xx_hal_dma_ex.o"
"stm32f407_uart_cmd\stm32f4xx_hal_dma.o"
"stm32f407_uart_cmd\stm32f4xx_hal_pwr.o"
"stm32f407_uart_cmd\stm32f4xx_hal_pwr_ex.o"
"stm32f407_uart_cmd\stm32f4xx_hal_cortex.o"
"stm32f407_uart_cmd\stm32f4xx_hal.o"
"stm32f407_uart_cmd\stm32f4xx_hal_exti.o"
"stm32f407_uart_cmd\system_stm32f4xx.o"
--library_type=microlib --strict --scatter "STM32F407_UART_CMD\STM32F407_UART_CMD.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "STM32F407_UART_CMD.map" -o STM32F407_UART_CMD\STM32F407_UART_CMD.axf