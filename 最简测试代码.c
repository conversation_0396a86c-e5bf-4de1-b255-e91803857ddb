/*
 * 最简电机测试代码 - 用于基础功能验证
 * 替换main.c中USER CODE BEGIN 2到USER CODE END 2的内容
 */

// 基础初始化
__HAL_UART_CLEAR_IDLEFLAG(&huart1);
__HAL_UART_ENABLE_IT(&huart1, UART_IT_IDLE);
HAL_UART_Receive_DMA(&huart1, (uint8_t *)rxCmd, CMD_LEN);

// 等待系统稳定
HAL_Delay(500);

// LED指示程序开始
HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED亮
HAL_Delay(1000);
HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);   // LED灭
HAL_Delay(1000);
HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED亮

// 发送最基础的原始命令
uint8_t cmd_enable[] = {0x01, 0xF3, 0xAB, 0x01, 0x00, 0x6B}; // 使能电机
HAL_UART_Transmit(&huart1, cmd_enable, 6, 1000);
HAL_Delay(2000);

uint8_t cmd_vel[] = {0x01, 0xF6, 0x00, 0x00, 0x32, 0x0A, 0x00, 0x6B}; // 速度50RPM
HAL_UART_Transmit(&huart1, cmd_vel, 8, 1000);
HAL_Delay(5000);

uint8_t cmd_stop[] = {0x01, 0xFE, 0x98, 0x00, 0x6B}; // 停止
HAL_UART_Transmit(&huart1, cmd_stop, 5, 1000);
HAL_Delay(1000);

uint8_t cmd_disable[] = {0x01, 0xF3, 0xAB, 0x00, 0x00, 0x6B}; // 禁用电机
HAL_UART_Transmit(&huart1, cmd_disable, 6, 1000);

// 测试完成，LED快闪
for(int i = 0; i < 10; i++) {
    HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
    HAL_Delay(200);
}

/*
这个代码的作用：
1. LED亮1秒表示程序开始
2. 发送原始字节命令（绕过函数调用）
3. 按顺序：使能->低速旋转->停止->禁用
4. LED快闪表示测试完成

如果这个都不工作，问题肯定在硬件层面：
- 串口连接
- 电机驱动器设置  
- 电源供电
- 电机本身

命令解析：
0x01 = 地址1
0xF3 0xAB = 使能命令
0x01/0x00 = 使能/禁用
0x6B = 校验字节

0xF6 = 速度控制命令
0x00 = CW方向
0x00 0x32 = 50RPM (0x0032)
0x0A = 加速度10
*/
