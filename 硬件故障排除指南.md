# 电机不动问题 - 硬件故障排除指南

## 🚨 紧急检查清单

### 1. 基础供电检查
- [ ] **STM32供电**：3.3V或5V正常
- [ ] **电机驱动器供电**：通常12V-48V，检查电压和电流容量
- [ ] **电机供电指示灯**：驱动器上的电源指示灯是否亮起
- [ ] **接地连接**：STM32 GND 与 驱动器 GND 必须连接

### 2. 串口连接检查
```
STM32F407          电机驱动器
PA9  (UART1_TX) -> RX
PA10 (UART1_RX) -> TX  
GND             -> GND
```
- [ ] **TX/RX连接**：确认没有接反
- [ ] **波特率匹配**：驱动器设置为115200
- [ ] **线材质量**：使用短而粗的连接线

### 3. 电机驱动器设置
- [ ] **设备地址**：设置为1（与代码中addr=1对应）
- [ ] **通讯协议**：UART模式，非CAN或其他协议
- [ ] **细分设置**：16细分（默认值）
- [ ] **使能状态**：驱动器是否处于使能状态

### 4. 电机连接检查
- [ ] **相序连接**：4线步进电机A+A-B+B-正确连接
- [ ] **电机规格**：电压电流是否匹配驱动器
- [ ] **机械负载**：电机轴是否被卡住

## 🔧 分步诊断流程

### 第一步：程序运行验证
1. **下载诊断代码**，观察LED指示：
   - 启动闪3次 → 程序正常
   - 无闪烁 → 程序未运行或硬件问题

2. **如果LED不闪烁**：
   - 检查程序下载是否成功
   - 检查复位电路
   - 检查晶振电路（8MHz外部晶振）

### 第二步：串口通讯验证
1. **使用示波器或逻辑分析仪**：
   - 检查PA9是否有数据输出
   - 波特率是否为115200
   - 数据格式：8N1（8位数据，无校验，1停止位）

2. **串口数据格式检查**：
   ```
   使能命令: 01 F3 AB 01 00 6B
   地址=1, 功能码=F3, 参数=AB, 使能=01, 同步=00, 校验=6B
   ```

3. **如果无串口输出**：
   - 检查UART1时钟是否使能
   - 检查GPIO复用功能配置
   - 检查DMA配置

### 第三步：电机驱动器响应测试
1. **观察驱动器指示灯**：
   - 通讯指示灯是否闪烁
   - 使能指示灯是否变化
   - 错误指示灯是否亮起

2. **手动测试电机**：
   - 断电状态下手动转动电机（应有阻力）
   - 上电后电机是否锁定

### 第四步：电机参数验证
1. **降低测试参数**：
   ```c
   // 极低速测试
   Emm_V5_Vel_Control(1, 0, 10, 1, false); // 10RPM
   ```

2. **检查脉冲计算**：
   ```
   16细分 × 200步/圈 = 3200脉冲/圈
   32000脉冲 = 10圈（可能过大）
   建议先测试800脉冲 = 1/4圈
   ```

## ⚡ 常见问题及解决方案

### 问题1：程序卡死，LED不闪烁
**可能原因**：
- 时钟配置错误
- 看门狗复位
- 硬件故障

**解决方案**：
```c
// 在main函数开始添加
HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // 立即点亮LED
while(1) {
    HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
    HAL_Delay(500);
} // 如果LED闪烁说明基础功能正常
```

### 问题2：LED正常，但串口无输出
**可能原因**：
- GPIO复用配置错误
- UART时钟未使能
- DMA配置问题

**解决方案**：
```c
// 检查UART配置
if (HAL_UART_Init(&huart1) != HAL_OK) {
    // 配置失败，LED快闪报错
    while(1) {
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
        HAL_Delay(100);
    }
}
```

### 问题3：串口有输出，电机不响应
**可能原因**：
- 驱动器地址不匹配
- 波特率不匹配
- 命令格式错误
- 驱动器故障

**解决方案**：
1. 尝试不同地址（1-255）
2. 检查驱动器手册确认命令格式
3. 使用驱动器厂商提供的测试软件验证

### 问题4：电机抖动但不转
**可能原因**：
- 电机相序错误
- 供电不足
- 负载过大
- 细分设置不当

**解决方案**：
1. 调整电机接线相序
2. 增大驱动器电流设置
3. 降低速度和加速度
4. 检查机械负载

## 🛠 高级调试工具

### 1. 示波器检查
- **PA9 (TX)**：应有115200波特率的数据
- **PA10 (RX)**：检查是否有返回数据
- **电机驱动信号**：检查驱动器输出到电机的脉冲

### 2. 逻辑分析仪
- 同时监控TX/RX数据
- 分析命令和响应的时序关系
- 检查数据完整性

### 3. 万用表检查
- 各级供电电压
- 电机绕组阻值（通常几欧姆到几十欧姆）
- 接地连续性

## 📋 最终检查表

在确认所有测试都失败后，按以下顺序检查：

1. [ ] **更换电机**：使用已知正常的电机测试
2. [ ] **更换驱动器**：使用已知正常的驱动器测试  
3. [ ] **更换连接线**：使用新的串口连接线
4. [ ] **检查STM32**：使用其他串口设备测试UART功能
5. [ ] **对比测试**：使用驱动器厂商的测试软件

## 🆘 技术支持

如果以上所有步骤都无法解决问题，请提供：
1. 电机型号和规格
2. 驱动器型号和设置截图
3. 硬件连接照片
4. 示波器波形截图（如有）
5. LED闪烁模式描述

**记住**：90%的电机不动问题都是硬件连接或配置问题，只有10%是代码问题。
